import * as THREE from 'three'
import { DataStructureBase, DataStructureNode, OperationResult, AlgorithmStep } from '../core/DataStructureBase'
import { SceneManager } from '../core/SceneManager'
import { AnimationSystem } from '../core/AnimationSystem'

interface BSTNode extends DataStructureNode {
  left?: BSTNode
  right?: BSTNode
  parent?: BSTNode
  level: number
}

/**
 * 二叉搜索树实现
 */
export class BinarySearchTree extends DataStructureBase {
  private root: BSTNode | null = null
  private nodeSpacing = 2
  private levelHeight = 2

  constructor(sceneManager: SceneManager, animationSystem: AnimationSystem) {
    super(sceneManager, animationSystem)
  }

  /**
   * 插入节点
   */
  public async insert(value: number): Promise<OperationResult> {
    if (this.isAnimating) {
      return { success: false, message: '操作正在进行中，请稍候' }
    }

    this.isAnimating = true

    try {
      const newNode = await this.insertNode(value)
      if (newNode) {
        await this.rebalanceLayout()
        this.isAnimating = false
        return { 
          success: true, 
          message: `成功插入节点 ${value}`,
          affectedNodes: [newNode]
        }
      } else {
        this.isAnimating = false
        return { success: false, message: `节点 ${value} 已存在` }
      }
    } catch (error) {
      this.isAnimating = false
      return { success: false, message: '插入操作失败' }
    }
  }

  /**
   * 删除节点
   */
  public async delete(value: number): Promise<OperationResult> {
    if (this.isAnimating) {
      return { success: false, message: '操作正在进行中，请稍候' }
    }

    this.isAnimating = true

    try {
      const deletedNode = await this.deleteNode(value)
      if (deletedNode) {
        await this.rebalanceLayout()
        this.isAnimating = false
        return { 
          success: true, 
          message: `成功删除节点 ${value}`,
          affectedNodes: [deletedNode]
        }
      } else {
        this.isAnimating = false
        return { success: false, message: `节点 ${value} 不存在` }
      }
    } catch (error) {
      this.isAnimating = false
      return { success: false, message: '删除操作失败' }
    }
  }

  /**
   * 搜索节点
   */
  public async search(value: number): Promise<OperationResult> {
    if (this.isAnimating) {
      return { success: false, message: '操作正在进行中，请稍候' }
    }

    this.isAnimating = true

    try {
      const foundNode = await this.searchNode(value)
      this.isAnimating = false
      
      if (foundNode) {
        return { 
          success: true, 
          message: `找到节点 ${value}`,
          affectedNodes: [foundNode]
        }
      } else {
        return { success: false, message: `节点 ${value} 不存在` }
      }
    } catch (error) {
      this.isAnimating = false
      return { success: false, message: '搜索操作失败' }
    }
  }

  /**
   * 清空树
   */
  public async clear(): Promise<void> {
    this.animationSystem.stopAllAnimations()
    
    // 淡出所有节点
    const fadePromises = Array.from(this.nodes.values()).map(node => 
      this.animationSystem.animateFadeOut(node.object3D)
    )
    
    await Promise.all(fadePromises)
    
    // 清理所有节点和边
    this.nodes.clear()
    this.edges.clear()
    this.root = null
    
    // 清空容器
    while (this.container.children.length > 0) {
      this.container.remove(this.container.children[0])
    }
  }

  /**
   * 获取支持的算法列表
   */
  public getAlgorithms(): string[] {
    return ['inorder', 'preorder', 'postorder', 'levelorder', 'search']
  }

  /**
   * 执行算法
   */
  public async executeAlgorithm(algorithmName: string, params?: any): Promise<AlgorithmStep[]> {
    switch (algorithmName) {
      case 'inorder':
        return this.inorderTraversal()
      case 'preorder':
        return this.preorderTraversal()
      case 'postorder':
        return this.postorderTraversal()
      case 'levelorder':
        return this.levelorderTraversal()
      case 'search':
        return this.searchTraversal(params?.value)
      default:
        return []
    }
  }

  /**
   * 插入节点的具体实现
   */
  private async insertNode(value: number): Promise<BSTNode | null> {
    // 检查是否已存在
    if (this.findNode(value)) {
      return null
    }

    const position = new THREE.Vector3(0, 0, 0)
    const object3D = this.createNodeObject(value, position)
    
    const newNode: BSTNode = {
      id: `node_${value}`,
      value,
      position,
      object3D,
      level: 0
    }

    // 先添加到场景但设为不可见
    object3D.visible = false
    this.addNodeToScene(newNode)

    if (!this.root) {
      // 第一个节点
      this.root = newNode
      newNode.level = 0
      newNode.position.set(0, 0, 0)
      object3D.position.copy(newNode.position)
      
      // 淡入动画
      await this.animationSystem.animateFadeIn(object3D)
    } else {
      // 找到插入位置
      const parent = this.findInsertPosition(value)
      if (!parent) return null

      newNode.parent = parent
      newNode.level = parent.level + 1

      if (value < parent.value) {
        parent.left = newNode
      } else {
        parent.right = newNode
      }

      // 计算初始位置（在父节点位置）
      newNode.position.copy(parent.position)
      object3D.position.copy(newNode.position)

      // 淡入动画
      await this.animationSystem.animateFadeIn(object3D)

      // 创建连接线
      const edgeId = `edge_${parent.id}_${newNode.id}`
      const edge = this.createEdge(parent.position, newNode.position)
      this.addEdgeToScene(edgeId, edge)
    }

    return newNode
  }

  /**
   * 删除节点的具体实现
   */
  private async deleteNode(value: number): Promise<BSTNode | null> {
    const nodeToDelete = this.findNode(value) as BSTNode
    if (!nodeToDelete) return null

    // 高亮要删除的节点
    await this.highlightNode(nodeToDelete.id, new THREE.Color(0xff4444))

    // 根据子节点数量处理删除
    if (!nodeToDelete.left && !nodeToDelete.right) {
      // 叶子节点
      await this.deleteLeafNode(nodeToDelete)
    } else if (!nodeToDelete.left || !nodeToDelete.right) {
      // 只有一个子节点
      await this.deleteNodeWithOneChild(nodeToDelete)
    } else {
      // 有两个子节点
      await this.deleteNodeWithTwoChildren(nodeToDelete)
    }

    return nodeToDelete
  }

  /**
   * 搜索节点的具体实现
   */
  private async searchNode(value: number): Promise<BSTNode | null> {
    let current = this.root
    const searchPath: BSTNode[] = []

    while (current) {
      searchPath.push(current)
      
      // 高亮当前节点
      await this.highlightNode(current.id, new THREE.Color(0xffff00))
      await new Promise(resolve => setTimeout(resolve, 500))

      if (value === current.value) {
        // 找到了，用绿色高亮
        await this.highlightNode(current.id, new THREE.Color(0x00ff00))
        return current
      } else if (value < current.value) {
        current = current.left
      } else {
        current = current.right
      }
    }

    return null
  }

  /**
   * 找到插入位置的父节点
   */
  private findInsertPosition(value: number): BSTNode | null {
    let current = this.root
    let parent: BSTNode | null = null

    while (current) {
      parent = current
      if (value < current.value) {
        current = current.left
      } else if (value > current.value) {
        current = current.right
      } else {
        return null // 值已存在
      }
    }

    return parent
  }

  /**
   * 查找节点
   */
  private findNode(value: number): BSTNode | null {
    let current = this.root
    while (current) {
      if (value === current.value) {
        return current
      } else if (value < current.value) {
        current = current.left
      } else {
        current = current.right
      }
    }
    return null
  }

  /**
   * 删除叶子节点
   */
  private async deleteLeafNode(node: BSTNode) {
    // 移除父节点的引用
    if (node.parent) {
      if (node.parent.left === node) {
        node.parent.left = undefined
      } else {
        node.parent.right = undefined
      }
      
      // 移除连接线
      const edgeId = `edge_${node.parent.id}_${node.id}`
      this.removeEdgeFromScene(edgeId)
    } else {
      this.root = null
    }

    // 淡出动画
    await this.animationSystem.animateFadeOut(node.object3D)
    this.removeNodeFromScene(node.id)
  }

  /**
   * 删除只有一个子节点的节点
   */
  private async deleteNodeWithOneChild(node: BSTNode) {
    const child = node.left || node.right!

    if (node.parent) {
      if (node.parent.left === node) {
        node.parent.left = child
      } else {
        node.parent.right = child
      }
      child.parent = node.parent
    } else {
      this.root = child
      child.parent = undefined
    }

    // 移除旧连接线
    if (node.parent) {
      const oldEdgeId = `edge_${node.parent.id}_${node.id}`
      this.removeEdgeFromScene(oldEdgeId)
    }
    
    const childEdgeId = `edge_${node.id}_${child.id}`
    this.removeEdgeFromScene(childEdgeId)

    // 淡出要删除的节点
    await this.animationSystem.animateFadeOut(node.object3D)
    this.removeNodeFromScene(node.id)
  }

  /**
   * 删除有两个子节点的节点
   */
  private async deleteNodeWithTwoChildren(node: BSTNode) {
    // 找到中序后继（右子树的最小值）
    const successor = this.findMinNode(node.right!)
    
    // 高亮后继节点
    await this.highlightNode(successor.id, new THREE.Color(0x00ffff))
    
    // 用后继节点的值替换当前节点的值
    node.value = successor.value
    
    // 更新节点显示
    this.updateNodeDisplay(node)
    
    // 删除后继节点（后继节点最多只有一个右子节点）
    if (successor.right) {
      await this.deleteNodeWithOneChild(successor)
    } else {
      await this.deleteLeafNode(successor)
    }
  }

  /**
   * 找到子树中的最小节点
   */
  private findMinNode(node: BSTNode): BSTNode {
    while (node.left) {
      node = node.left
    }
    return node
  }

  /**
   * 更新节点显示
   */
  private updateNodeDisplay(node: BSTNode) {
    // 更新文本显示
    const textMesh = node.object3D.children.find(child => 
      child instanceof THREE.Mesh && child.material instanceof THREE.MeshBasicMaterial
    ) as THREE.Mesh
    
    if (textMesh) {
      const newGeometry = this.createTextGeometry(node.value.toString())
      textMesh.geometry.dispose()
      textMesh.geometry = newGeometry
    }
  }

  /**
   * 重新平衡布局
   */
  private async rebalanceLayout() {
    if (!this.root) return

    // 计算每个节点的新位置
    this.calculatePositions(this.root, 0, 0)

    // 更新所有连接线
    this.updateAllEdges()

    // 动画移动到新位置
    const movePromises = Array.from(this.nodes.values()).map(node => {
      return this.animationSystem.animateMove(node.object3D, {
        to: node.position,
        duration: 1,
        ease: 'power2.out'
      })
    })

    await Promise.all(movePromises)
  }

  /**
   * 计算节点位置
   */
  private calculatePositions(node: BSTNode, x: number, y: number) {
    node.position.set(x, y, 0)
    
    const leftWidth = this.getSubtreeWidth(node.left)
    const rightWidth = this.getSubtreeWidth(node.right)
    
    if (node.left) {
      this.calculatePositions(
        node.left, 
        x - this.nodeSpacing * (1 + rightWidth / 2), 
        y - this.levelHeight
      )
    }
    
    if (node.right) {
      this.calculatePositions(
        node.right, 
        x + this.nodeSpacing * (1 + leftWidth / 2), 
        y - this.levelHeight
      )
    }
  }

  /**
   * 获取子树宽度
   */
  private getSubtreeWidth(node: BSTNode | undefined): number {
    if (!node) return 0
    return 1 + this.getSubtreeWidth(node.left) + this.getSubtreeWidth(node.right)
  }

  /**
   * 更新所有连接线
   */
  private updateAllEdges() {
    this.edges.forEach((edge, edgeId) => {
      const [parentId, childId] = edgeId.replace('edge_', '').split('_')
      const parent = this.nodes.get(parentId)
      const child = this.nodes.get(childId)
      
      if (parent && child) {
        this.updateEdge(edge, parent.position, child.position)
      }
    })
  }

  // 遍历算法实现
  private async inorderTraversal(): Promise<AlgorithmStep[]> {
    const steps: AlgorithmStep[] = []
    const visited: string[] = []

    const traverse = (node: BSTNode | undefined, stepId: number): number => {
      if (!node) return stepId

      // 访问左子树
      stepId = traverse(node.left, stepId)

      // 访问当前节点
      visited.push(node.id)
      steps.push({
        id: `step_${stepId++}`,
        description: `访问节点 ${node.value}`,
        currentNode: node.id,
        visitedNodes: [...visited],
        action: 'visit'
      })

      // 访问右子树
      stepId = traverse(node.right, stepId)

      return stepId
    }

    traverse(this.root, 0)
    return steps
  }

  private async preorderTraversal(): Promise<AlgorithmStep[]> {
    const steps: AlgorithmStep[] = []
    const visited: string[] = []

    const traverse = (node: BSTNode | undefined, stepId: number): number => {
      if (!node) return stepId

      // 访问当前节点
      visited.push(node.id)
      steps.push({
        id: `step_${stepId++}`,
        description: `访问节点 ${node.value}`,
        currentNode: node.id,
        visitedNodes: [...visited],
        action: 'visit'
      })

      // 访问左子树
      stepId = traverse(node.left, stepId)

      // 访问右子树
      stepId = traverse(node.right, stepId)

      return stepId
    }

    traverse(this.root, 0)
    return steps
  }

  private async postorderTraversal(): Promise<AlgorithmStep[]> {
    const steps: AlgorithmStep[] = []
    const visited: string[] = []

    const traverse = (node: BSTNode | undefined, stepId: number): number => {
      if (!node) return stepId

      // 访问左子树
      stepId = traverse(node.left, stepId)

      // 访问右子树
      stepId = traverse(node.right, stepId)

      // 访问当前节点
      visited.push(node.id)
      steps.push({
        id: `step_${stepId++}`,
        description: `访问节点 ${node.value}`,
        currentNode: node.id,
        visitedNodes: [...visited],
        action: 'visit'
      })

      return stepId
    }

    traverse(this.root, 0)
    return steps
  }

  private async levelorderTraversal(): Promise<AlgorithmStep[]> {
    const steps: AlgorithmStep[] = []
    const visited: string[] = []
    
    if (!this.root) return steps

    const queue: BSTNode[] = [this.root]
    let stepId = 0

    while (queue.length > 0) {
      const node = queue.shift()!
      visited.push(node.id)
      
      steps.push({
        id: `step_${stepId++}`,
        description: `访问节点 ${node.value}`,
        currentNode: node.id,
        visitedNodes: [...visited],
        action: 'visit'
      })

      if (node.left) queue.push(node.left)
      if (node.right) queue.push(node.right)
    }

    return steps
  }

  private async searchTraversal(value: number): Promise<AlgorithmStep[]> {
    const steps: AlgorithmStep[] = []
    let current = this.root
    let stepId = 0

    while (current) {
      steps.push({
        id: `step_${stepId++}`,
        description: `比较节点 ${current.value} 与目标值 ${value}`,
        currentNode: current.id,
        action: 'compare'
      })

      if (value === current.value) {
        steps.push({
          id: `step_${stepId++}`,
          description: `找到目标值 ${value}`,
          currentNode: current.id,
          action: 'complete'
        })
        break
      } else if (value < current.value) {
        steps.push({
          id: `step_${stepId++}`,
          description: `${value} < ${current.value}，向左子树搜索`,
          currentNode: current.id,
          action: 'move'
        })
        current = current.left
      } else {
        steps.push({
          id: `step_${stepId++}`,
          description: `${value} > ${current.value}，向右子树搜索`,
          currentNode: current.id,
          action: 'move'
        })
        current = current.right
      }
    }

    if (!current) {
      steps.push({
        id: `step_${stepId++}`,
        description: `未找到目标值 ${value}`,
        action: 'complete'
      })
    }

    return steps
  }
}
